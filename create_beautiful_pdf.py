#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def create_colorful_html():
    # إنشاء محتوى HTML مع CSS جميل ومبهر وطفولي
    html_content = """
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>سكريبت شركات الهواتف الصاعدة</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@400;600;700&display=swap');
            
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Cairo', 'Amiri', Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: #2c3e50;
                line-height: 1.4;
                padding: 15px;
                direction: rtl;
                font-size: 13px;
            }
            
            .container {
                max-width: 750px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                overflow: hidden;
                position: relative;
            }
            
            .header {
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
                color: white;
                text-align: center;
                padding: 20px 15px;
                position: relative;
                overflow: hidden;
            }
            
            .header::before {
                content: '🌟✨🎬✨🌟';
                position: absolute;
                top: 5px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 20px;
                animation: sparkle 2s ease-in-out infinite;
            }
            
            @keyframes sparkle {
                0%, 100% { opacity: 0.7; transform: translateX(-50%) scale(1); }
                50% { opacity: 1; transform: translateX(-50%) scale(1.1); }
            }
            
            .main-title {
                font-size: 22px;
                font-weight: 700;
                margin-bottom: 8px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                position: relative;
                z-index: 1;
                margin-top: 15px;
            }
            
            .content {
                padding: 20px;
            }
            
            .slide {
                margin-bottom: 18px;
                padding: 15px;
                border-radius: 15px;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-right: 4px solid;
                position: relative;
                overflow: hidden;
            }
            
            .slide:nth-child(1) { border-right-color: #ff6b6b; }
            .slide:nth-child(2) { border-right-color: #4ecdc4; }
            .slide:nth-child(3) { border-right-color: #45b7d1; }
            .slide:nth-child(4) { border-right-color: #f39c12; }
            .slide:nth-child(5) { border-right-color: #9b59b6; }
            
            .slide::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 60px;
                height: 60px;
                background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
                border-radius: 50%;
                transform: translate(20px, -20px);
            }
            
            .slide-title {
                font-size: 15px;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 10px;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .slide-content {
                font-size: 12px;
                line-height: 1.6;
                margin-bottom: 8px;
            }
            
            .highlight {
                background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
                padding: 8px 12px;
                border-radius: 10px;
                font-weight: 600;
                margin: 8px 0;
                border-right: 3px solid #ff6b6b;
                font-size: 12px;
            }
            
            .note {
                color: #7f8c8d;
                font-style: italic;
                font-size: 11px;
                margin-top: 6px;
                padding: 6px 10px;
                background: rgba(127, 140, 141, 0.1);
                border-radius: 8px;
            }
            
            .features-list {
                list-style: none;
                padding: 0;
                margin: 8px 0;
            }
            
            .features-list li {
                padding: 5px 0;
                position: relative;
                padding-right: 20px;
                font-size: 12px;
            }
            
            .features-list li::before {
                content: '✓';
                position: absolute;
                right: 0;
                color: #27ae60;
                font-weight: bold;
                font-size: 14px;
            }
            
            .notes-section {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                margin-top: 15px;
                border-radius: 15px;
            }
            
            .notes-title {
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 15px;
                text-align: center;
            }
            
            .notes-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }
            
            .note-card {
                background: rgba(255, 255, 255, 0.1);
                padding: 12px;
                border-radius: 10px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            
            .note-card h4 {
                font-size: 12px;
                margin-bottom: 6px;
                color: #ffd700;
            }
            
            .note-card p {
                font-size: 10px;
                line-height: 1.4;
            }
            
            .emoji {
                font-size: 18px;
                margin-left: 8px;
            }
            
            @media print {
                body {
                    background: white;
                    padding: 0;
                    font-size: 12px;
                }
                .container {
                    box-shadow: none;
                    border-radius: 0;
                    max-width: 100%;
                }
                .header {
                    padding: 15px;
                }
                .content {
                    padding: 15px;
                }
                .slide {
                    margin-bottom: 12px;
                    padding: 10px;
                }
                .notes-section {
                    padding: 15px;
                }
                .notes-grid {
                    gap: 8px;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="main-title">🎬 سكريبت: شركات الهواتف الصاعدة (40 ثانية)</h1>
            </div>
            
            <div class="content">
                <div class="slide">
                    <h2 class="slide-title">📱 الشريحة 1: العنوان الرئيسي (5 ثوان)</h2>
                    <div class="highlight">"هل تعلم أن عالم الهواتف الذكية يشهد ثورة حقيقية؟"</div>
                    <div class="note">[صوت موسيقى تشويقية خفيفة]</div>
                </div>
                
                <div class="slide">
                    <h2 class="slide-title">📈 الشريحة 2: إحصائية مذهلة (8 ثوان)</h2>
                    <div class="highlight">"في عام 2024، نمت شركات الهواتف الناشئة بنسبة 300%!"</div>
                    <div class="slide-content">"شركات جديدة تتحدى العمالقة بتقنيات مبتكرة وأسعار منافسة"</div>
                    <div class="note">[رسم بياني متحرك يظهر النمو]</div>
                </div>
                
                <div class="slide">
                    <h2 class="slide-title">🚀 الشريحة 3: أمثلة ملهمة (12 ثانية)</h2>
                    <div class="slide-content">"Nothing Phone - بتصميمها الشفاف الثوري"</div>
                    <div class="slide-content">"OnePlus - من شركة ناشئة إلى منافس عالمي"</div>
                    <div class="slide-content">"Realme - تقنيات متطورة بأسعار معقولة"</div>
                    <div class="slide-content">"Poco - قوة الأداء بميزانية محدودة"</div>
                    <div class="note">[صور متحركة للهواتف مع مؤثرات بصرية]</div>
                </div>
                
                <div class="slide">
                    <h2 class="slide-title">💡 الشريحة 4: الابتكار والتميز (10 ثوان)</h2>
                    <div class="highlight">"هذه الشركات لا تقلد... بل تبتكر!"</div>
                    <ul class="features-list">
                        <li>شحن فائق السرعة 150 واط</li>
                        <li>كاميرات بدقة 200 ميجابكسل</li>
                        <li>تصاميم جريئة ومختلفة</li>
                    </ul>
                    <div class="note">[أيقونات متحركة تظهر الميزات]</div>
                </div>
                
                <div class="slide">
                    <h2 class="slide-title">🎯 الشريحة 5: الخاتمة والدعوة للعمل (5 ثوان)</h2>
                    <div class="highlight">"المستقبل في أيدي الجرأة والابتكار"</div>
                    <div class="highlight">"هل أنت مستعد لتجربة الجيل الجديد؟"</div>
                    <div class="note">[شعار ختامي مع موسيقى ملهمة]</div>
                </div>
            </div>
            
            <div class="notes-section">
                <h3 class="notes-title">📝 ملاحظات للعرض</h3>
                <div class="notes-grid">
                    <div class="note-card">
                        <h4>🎨 التصميم البصري</h4>
                        <p>ألوان عصرية: أزرق تقني، برتقالي نابض، أبيض نظيف<br>
                        خطوط عربية واضحة وعصرية<br>
                        انتقالات سلسة بين الشرائح</p>
                    </div>
                    <div class="note-card">
                        <h4>🎵 المؤثرات الصوتية</h4>
                        <p>موسيقى تقنية حديثة في الخلفية<br>
                        أصوات "بيب" عند ظهور الإحصائيات<br>
                        تأثيرات "ووش" عند الانتقالات</p>
                    </div>
                    <div class="note-card">
                        <h4>⏱️ التوقيت</h4>
                        <p>إجمالي المدة: 40 ثانية بالضبط<br>
                        كل شريحة لها توقيت محدد<br>
                        إيقاع سريع ومشوق</p>
                    </div>
                    <div class="note-card">
                        <h4>🎭 نصائح للأداء</h4>
                        <p>صوت حماسي ومتفائل<br>
                        تنويع في نبرة الصوت<br>
                        التركيز على الكلمات المفتاحية</p>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    
    # حفظ ملف HTML
    html_filename = "سكريبت_شركات_الهواتف_الصاعدة.html"
    with open(html_filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ تم إنشاء ملف HTML بنجاح: {html_filename}")
    print("📄 يمكنك فتح الملف في المتصفح وطباعته كـ PDF")
    print("🖨️ للطباعة: اختر 'طباعة' من المتصفح ثم 'حفظ كـ PDF'")
    print("📋 تأكد من اختيار 'صفحة واحدة' في إعدادات الطباعة")
    
    return html_filename

if __name__ == "__main__":
    create_colorful_html()

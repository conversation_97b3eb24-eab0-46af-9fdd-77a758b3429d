import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def create_phones_excel():
    # إنشاء ملف Excel جديد
    wb = Workbook()
    
    # حذف الشيت الافتراضي
    wb.remove(wb.active)
    
    # تعريف الألوان والأنماط
    header_fill = PatternFill(start_color="2E86AB", end_color="2E86AB", fill_type="solid")  # أزرق تقني
    accent_fill = PatternFill(start_color="F24236", end_color="F24236", fill_type="solid")  # برتقالي نابض
    light_fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")  # رمادي فاتح
    success_fill = PatternFill(start_color="28A745", end_color="28A745", fill_type="solid")  # أخضر
    
    header_font = Font(name="Arabic Typesetting", size=16, bold=True, color="FFFFFF")
    title_font = Font(name="Arabic Typesetting", size=14, bold=True, color="2E86AB")
    content_font = Font(name="Arabic Typesetting", size=12, color="333333")
    accent_font = Font(name="Arabic Typesetting", size=12, bold=True, color="F24236")
    
    center_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
    right_alignment = Alignment(horizontal="right", vertical="center", wrap_text=True)
    
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # الشيت الأول: العنوان الرئيسي
    ws1 = wb.create_sheet("🎬 العنوان الرئيسي")
    
    # تنسيق العنوان الرئيسي
    ws1.merge_cells('A1:D3')
    ws1['A1'] = "🚀 شركات الهواتف الصاعدة"
    ws1['A1'].font = Font(name="Arabic Typesetting", size=24, bold=True, color="FFFFFF")
    ws1['A1'].fill = header_fill
    ws1['A1'].alignment = center_alignment
    
    ws1.merge_cells('A5:D6')
    ws1['A5'] = "هل تعلم أن عالم الهواتف الذكية يشهد ثورة حقيقية؟"
    ws1['A5'].font = Font(name="Arabic Typesetting", size=16, bold=True, color="2E86AB")
    ws1['A5'].alignment = center_alignment
    
    ws1.merge_cells('A8:D9')
    ws1['A8'] = "⏱️ مدة العرض: 40 ثانية"
    ws1['A8'].font = accent_font
    ws1['A8'].fill = light_fill
    ws1['A8'].alignment = center_alignment
    
    # تعديل عرض الأعمدة
    for col in ['A', 'B', 'C', 'D']:
        ws1.column_dimensions[col].width = 20
    
    # الشيت الثاني: الإحصائيات
    ws2 = wb.create_sheet("📈 إحصائيات مذهلة")
    
    # عنوان الشيت
    ws2.merge_cells('A1:D2')
    ws2['A1'] = "📈 نمو شركات الهواتف الناشئة"
    ws2['A1'].font = header_font
    ws2['A1'].fill = header_fill
    ws2['A1'].alignment = center_alignment
    
    # الإحصائية الرئيسية
    ws2.merge_cells('A4:D5')
    ws2['A4'] = "300%"
    ws2['A4'].font = Font(name="Arabic Typesetting", size=36, bold=True, color="F24236")
    ws2['A4'].alignment = center_alignment
    ws2['A4'].fill = light_fill
    
    ws2.merge_cells('A6:D7')
    ws2['A6'] = "نسبة النمو في عام 2024"
    ws2['A6'].font = title_font
    ws2['A6'].alignment = center_alignment
    
    # معلومات إضافية
    ws2['A9'] = "✅"
    ws2['B9'] = "تقنيات مبتكرة"
    ws2['A10'] = "✅"
    ws2['B10'] = "أسعار منافسة"
    ws2['A11'] = "✅"
    ws2['B11'] = "تحدي العمالقة"
    
    for row in range(9, 12):
        ws2[f'A{row}'].font = accent_font
        ws2[f'A{row}'].alignment = center_alignment
        ws2[f'B{row}'].font = content_font
        ws2[f'B{row}'].alignment = right_alignment
    
    for col in ['A', 'B', 'C', 'D']:
        ws2.column_dimensions[col].width = 20
    
    # الشيت الثالث: أمثلة الشركات
    ws3 = wb.create_sheet("🚀 شركات ملهمة")
    
    # عنوان الشيت
    ws3.merge_cells('A1:D2')
    ws3['A1'] = "🚀 شركات تقود المستقبل"
    ws3['A1'].font = header_font
    ws3['A1'].fill = header_fill
    ws3['A1'].alignment = center_alignment
    
    # بيانات الشركات
    companies_data = [
        ["📱", "Nothing Phone", "تصميم شفاف ثوري", "🌟🌟🌟🌟🌟"],
        ["⚡", "OnePlus", "من ناشئة إلى عملاق", "🌟🌟🌟🌟🌟"],
        ["💎", "Realme", "تقنيات متطورة بأسعار معقولة", "🌟🌟🌟🌟"],
        ["🔥", "Poco", "قوة الأداء بميزانية محدودة", "🌟🌟🌟🌟"]
    ]
    
    # إضافة عناوين الأعمدة
    headers = ["الرمز", "اسم الشركة", "المميزات", "التقييم"]
    for col, header in enumerate(headers, 1):
        cell = ws3.cell(row=4, column=col, value=header)
        cell.font = Font(name="Arabic Typesetting", size=12, bold=True, color="FFFFFF")
        cell.fill = accent_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # إضافة بيانات الشركات
    for row, company in enumerate(companies_data, 5):
        for col, value in enumerate(company, 1):
            cell = ws3.cell(row=row, column=col, value=value)
            cell.font = content_font
            cell.alignment = center_alignment if col in [1, 4] else right_alignment
            cell.border = thin_border
            if row % 2 == 0:
                cell.fill = light_fill
    
    for col in ['A', 'B', 'C', 'D']:
        ws3.column_dimensions[col].width = 25
    
    # الشيت الرابع: الابتكارات
    ws4 = wb.create_sheet("💡 الابتكار والتميز")
    
    # عنوان الشيت
    ws4.merge_cells('A1:D2')
    ws4['A1'] = "💡 ابتكارات تغير اللعبة"
    ws4['A1'].font = header_font
    ws4['A1'].fill = header_fill
    ws4['A1'].alignment = center_alignment
    
    ws4.merge_cells('A4:D4')
    ws4['A4'] = "هذه الشركات لا تقلد... بل تبتكر!"
    ws4['A4'].font = title_font
    ws4['A4'].alignment = center_alignment
    
    # الابتكارات
    innovations = [
        ["⚡", "شحن فائق السرعة", "150 واط", "🔋"],
        ["📸", "كاميرات عالية الدقة", "200 ميجابكسل", "📷"],
        ["🎨", "تصاميم جريئة", "مختلفة ومبتكرة", "✨"]
    ]
    
    for row, innovation in enumerate(innovations, 6):
        ws4[f'A{row}'] = innovation[0]
        ws4[f'B{row}'] = innovation[1]
        ws4[f'C{row}'] = innovation[2]
        ws4[f'D{row}'] = innovation[3]
        
        ws4[f'A{row}'].font = Font(name="Arabic Typesetting", size=16, color="F24236")
        ws4[f'A{row}'].alignment = center_alignment
        ws4[f'B{row}'].font = content_font
        ws4[f'B{row}'].alignment = right_alignment
        ws4[f'C{row}'].font = accent_font
        ws4[f'C{row}'].alignment = center_alignment
        ws4[f'D{row}'].font = Font(name="Arabic Typesetting", size=16, color="28A745")
        ws4[f'D{row}'].alignment = center_alignment
        
        if row % 2 == 0:
            for col in ['A', 'B', 'C', 'D']:
                ws4[f'{col}{row}'].fill = light_fill
    
    for col in ['A', 'B', 'C', 'D']:
        ws4.column_dimensions[col].width = 20
    
    # الشيت الخامس: الخاتمة
    ws5 = wb.create_sheet("🎯 الخاتمة الملهمة")
    
    # عنوان الشيت
    ws5.merge_cells('A1:D2')
    ws5['A1'] = "🎯 المستقبل في أيدي الجرأة"
    ws5['A1'].font = header_font
    ws5['A1'].fill = success_fill
    ws5['A1'].alignment = center_alignment
    
    ws5.merge_cells('A4:D5')
    ws5['A4'] = "المستقبل في أيدي الجرأة والابتكار"
    ws5['A4'].font = Font(name="Arabic Typesetting", size=18, bold=True, color="28A745")
    ws5['A4'].alignment = center_alignment
    
    ws5.merge_cells('A7:D8')
    ws5['A7'] = "هل أنت مستعد لتجربة الجيل الجديد؟"
    ws5['A7'].font = Font(name="Arabic Typesetting", size=16, bold=True, color="F24236")
    ws5['A7'].alignment = center_alignment
    ws5['A7'].fill = light_fill
    
    # دعوة للعمل
    ws5.merge_cells('A10:D11')
    ws5['A10'] = "🚀 ابدأ رحلتك مع التقنية الجديدة اليوم!"
    ws5['A10'].font = Font(name="Arabic Typesetting", size=14, bold=True, color="FFFFFF")
    ws5['A10'].fill = accent_fill
    ws5['A10'].alignment = center_alignment
    
    for col in ['A', 'B', 'C', 'D']:
        ws5.column_dimensions[col].width = 25
    
    # حفظ الملف
    filename = "شركات_الهواتف_الصاعدة_عرض_تفاعلي.xlsx"
    wb.save(filename)
    print(f"✅ تم إنشاء ملف Excel بنجاح: {filename}")
    
    return filename

if __name__ == "__main__":
    create_phones_excel()
